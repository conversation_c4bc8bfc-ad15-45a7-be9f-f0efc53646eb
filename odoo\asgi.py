# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Odoo ASGI application

This module provides the ASGI application for Odoo, replacing the legacy WSGI
implementation with modern async capabilities while maintaining backward compatibility.
"""

import asyncio
import json
import logging
import threading
import time
import os
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any, Callable, Awaitable, Optional

from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request as StarletteRequest
from starlette.responses import Response as StarletteResponse, FileResponse, RedirectResponse
from starlette.routing import Route, Mount, WebSocketRoute
from starlette.staticfiles import StaticFiles

import odoo
from odoo import http, api, modules
from odoo.tools import config
from odoo.tools.func import lazy_property
from odoo.exceptions import UserError, AccessError
from odoo.http import SessionExpiredException
from werkzeug.routing import Map, Rule
from werkzeug.exceptions import NotFound

_logger = logging.getLogger(__name__)


class ASGIRequest:
    """
    ASGI Request wrapper that maintains compatibility with existing Odoo Request interface
    """
    
    def __init__(self, starlette_request: StarletteRequest):
        self.starlette_request = starlette_request
        self._setup_compatibility()
    
    def _setup_compatibility(self):
        """Setup compatibility attributes for existing Odoo code"""
        # Create a mock HTTPRequest that mimics the old interface
        self.httprequest = self._create_http_request_mock()
        self.future_response = http.FutureResponse()
        self.dispatcher = http._dispatchers['http'](self)
        
        # Setup other required attributes
        self.geoip = http.GeoIP(self.starlette_request.client.host if self.starlette_request.client else '127.0.0.1')
        self.registry = None
        self.env = None
        self.params = dict(self.starlette_request.query_params)
        self.session = None
        self.db = None
    
    def _create_http_request_mock(self):
        """Create a mock HTTPRequest object for compatibility"""
        class HTTPRequestMock:
            def __init__(self, starlette_req):
                self.starlette_req = starlette_req
                self.method = starlette_req.method
                self.url = str(starlette_req.url)
                self.path = starlette_req.url.path
                self.query_string = starlette_req.url.query.encode() if starlette_req.url.query else b''
                self.headers = dict(starlette_req.headers)
                self.remote_addr = starlette_req.client.host if starlette_req.client else '127.0.0.1'
                self.args = dict(starlette_req.query_params)
                self.cookies = dict(starlette_req.cookies)

                # Add form and files attributes for compatibility
                self.form = {}  # Will be populated during _post_init
                self.files = {}  # Will be populated during _post_init

            @property
            def mimetype(self):
                return self.headers.get('content-type', '').split(';')[0]

        return HTTPRequestMock(self.starlette_request)
    
    async def _post_init(self):
        """Async version of post init"""
        self.session, self.db = await self._get_session_and_dbname()

        # Initialize cached data
        self._form_data = {}
        self._json_data = {}

        # Update params with form data if it's a POST request
        if self.httprequest.method in ['POST', 'PUT', 'PATCH']:
            try:
                form_data = await self.starlette_request.form()
                self._form_data = {}
                files_data = {}

                # Separate form fields and files
                for key, value in form_data.items():
                    if hasattr(value, 'filename'):  # It's a file upload
                        files_data[key] = value
                    else:
                        self._form_data[key] = value

                # Update httprequest mock with form and files data
                self.httprequest.form = self._form_data
                self.httprequest.files = files_data

                self.params.update(self._form_data)
            except Exception:
                # If form parsing fails, try JSON
                try:
                    json_data = await self.starlette_request.json()
                    if isinstance(json_data, dict):
                        self._json_data = json_data
                        self.params.update(json_data)
                except Exception:
                    pass
    
    async def _get_session_and_dbname(self):
        """Async version of session and db name retrieval"""
        # Get session from cookies
        session_id = self.httprequest.cookies.get('session_id')
        session = None
        db = None

        # Get session store
        session_store = http.root.session_store

        if session_id and session_store.is_valid_key(session_id):
            try:
                # Load session from session store
                session = session_store.get(session_id)
                if session and hasattr(session, 'db'):
                    db = session.db
                # Ensure session ID is set in case it wasn't persisted
                session.sid = session_id
            except Exception:
                _logger.debug("Failed to load session", exc_info=True)
                session = None

        # Create a new session if none exists or loading failed
        if session is None:
            session = session_store.new()

        # Apply default session values
        default_session = http.get_default_session()
        for key, val in default_session.items():
            session.setdefault(key, val)

        # Set default language if not present
        if not session.context.get('lang'):
            session.context['lang'] = self.default_lang()

        # Database handling - follow the same pattern as synchronous version
        dbname = None
        host = self.httprequest.environ.get('HTTP_HOST', '')

        # Check if session has a valid database
        if session.db and http.db_filter([session.db], host=host):
            dbname = session.db
        else:
            # Try to get database from available databases
            try:
                all_dbs = http.db_list(force=True, host=host)
                if len(all_dbs) == 1:
                    dbname = all_dbs[0]  # monodb
            except Exception:
                pass

        # Handle database changes
        if session.db != dbname:
            if session.db:
                _logger.warning("Logged into database %r, but dbfilter rejects it; logging session out.", session.db)
                session.logout(keep_db=False)
            session.db = dbname

        return session, dbname

    def get_http_params(self):
        """
        Extract key=value pairs from the query string and the forms
        present in the body (both application/x-www-form-urlencoded and
        multipart/form-data).

        :returns: The merged key-value pairs.
        :rtype: dict
        """
        # Start with query parameters
        params = dict(self.starlette_request.query_params)

        # Add form data if available (this will be populated during _post_init)
        if hasattr(self, '_form_data') and self._form_data:
            params.update(self._form_data)

        return params

    def get_json_data(self):
        """
        Get JSON data from request body.

        :returns: Parsed JSON data
        :rtype: dict
        """
        if hasattr(self, '_json_data') and self._json_data:
            return self._json_data

        # Fallback to synchronous parsing (not recommended for ASGI)
        try:
            import asyncio
            import json
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're in an async context, we can't use run_until_complete
                # Return empty dict and log warning
                _logger.warning("get_json_data called in async context without cached data")
                return {}
            else:
                # Synchronous context
                body = loop.run_until_complete(self.starlette_request.body())
                return json.loads(body.decode('utf-8'))
        except Exception as e:
            _logger.debug("Failed to parse JSON data: %s", e)
            return {}

    def _set_request_dispatcher(self, rule):
        """Set the request dispatcher based on the routing rule"""
        # This mimics the behavior from the WSGI implementation
        if hasattr(rule, 'endpoint') and hasattr(rule.endpoint, 'routing'):
            routing = rule.endpoint.routing
            if 'type' in routing:
                dispatcher_type = routing['type']
                if dispatcher_type in http._dispatchers:
                    self.dispatcher = http._dispatchers[dispatcher_type](self)

    def _save_session(self):
        """Save a modified session on disk."""
        sess = self.session

        if not sess or not sess.can_save:
            return

        if sess.should_rotate:
            http.root.session_store.rotate(sess, self.env)  # it saves
        elif sess.is_dirty:
            http.root.session_store.save(sess)

        cookie_sid = self.httprequest.cookies.get('session_id')
        if sess.is_dirty or cookie_sid != sess.sid:
            # Set session cookie in future response
            max_age = http.get_session_max_inactivity(self.env) if self.env else None
            self.future_response.set_cookie('session_id', sess.sid, max_age=max_age, httponly=True)

    def _inject_future_response(self, response):
        """Inject future response headers and cookies"""
        if hasattr(response, 'headers') and hasattr(self.future_response, 'headers'):
            response.headers.extend(self.future_response.headers)
        return response

    @property
    def best_lang(self):
        """Get the best language from Accept-Language header"""
        accept_language = self.httprequest.headers.get('accept-language', '')
        if not accept_language:
            return None

        # Simple parsing of Accept-Language header
        # Format: "en-US,en;q=0.9,fr;q=0.8"
        languages = []
        for lang_entry in accept_language.split(','):
            lang_entry = lang_entry.strip()
            if ';' in lang_entry:
                lang, quality = lang_entry.split(';', 1)
                try:
                    q = float(quality.split('=')[1])
                except (IndexError, ValueError):
                    q = 1.0
            else:
                lang, q = lang_entry, 1.0

            lang = lang.strip()
            if lang:
                # Convert from HTTP format (en-US) to Odoo format (en_US)
                lang = lang.replace('-', '_')
                languages.append((q, lang))

        if not languages:
            return None

        # Sort by quality and return the best one
        languages.sort(reverse=True)
        return languages[0][1]

    def default_lang(self):
        """Returns default user language according to request specification

        :returns: Preferred language if specified or 'en_US'
        :rtype: str
        """
        return self.best_lang or http.DEFAULT_LANG

    def default_context(self):
        """Returns default context"""
        return {'lang': self.default_lang()}

    def redirect(self, location, code=303, local=True):
        """
        Returns a response object with a redirect to the given location.

        :param location: the location to redirect to
        :param code: the redirect status code
        :param local: if True, only allow local redirects
        :returns: RedirectResponse
        """
        from starlette.responses import RedirectResponse

        if local and location.startswith('http'):
            # For local redirects, ensure we're redirecting to the same host
            from urllib.parse import urlparse
            parsed = urlparse(location)
            if parsed.netloc and parsed.netloc != self.starlette_request.url.netloc:
                raise ValueError("Non-local redirect not allowed")

        return RedirectResponse(url=location, status_code=code)

    def redirect_query(self, location, query=None, code=303, local=True):
        """
        Returns a response object with a redirect to the given location with query parameters.

        :param location: the location to redirect to
        :param query: query parameters to add to the location
        :param code: the redirect status code
        :param local: if True, only allow local redirects
        :returns: RedirectResponse
        """
        if query:
            from urllib.parse import urlencode
            separator = '&' if '?' in location else '?'
            location += separator + urlencode(query)
        return self.redirect(location, code=code, local=local)

    def render(self, template, qcontext=None, lazy=True, **kw):
        """
        Render a template with the given context.

        :param template: template name
        :param qcontext: template context
        :param lazy: whether to use lazy rendering
        :returns: rendered template response
        """
        # This is a simplified implementation
        # In a real implementation, this would use Odoo's template engine
        from starlette.responses import HTMLResponse
        return HTMLResponse(f"<html><body>Template: {template}</body></html>")

    def not_found(self, description=None):
        """
        Returns a 404 Not Found response.

        :param description: optional description
        :returns: 404 response
        """
        from starlette.responses import Response
        content = description or "Not Found"
        return Response(content=content, status_code=404)


class OdooASGIMiddleware(BaseHTTPMiddleware):
    """
    Main ASGI middleware that handles Odoo request processing
    """
    
    def __init__(self, app, odoo_app):
        super().__init__(app)
        self.odoo_app = odoo_app
    
    async def dispatch(self, request: StarletteRequest, call_next: Callable) -> StarletteResponse:
        """
        Main request dispatch method
        """
        # Setup thread-local variables for compatibility
        current_thread = threading.current_thread()
        current_thread.query_count = 0
        current_thread.query_time = 0
        current_thread.perf_t0 = time.time()
        current_thread.cursor_mode = None
        
        # Clean up thread-local attributes
        for attr in ['dbname', 'uid']:
            if hasattr(current_thread, attr):
                delattr(current_thread, attr)
        
        try:
            # Create Odoo request wrapper
            odoo_request = ASGIRequest(request)
            
            # Setup request context
            http._request_stack.push(odoo_request)
            
            try:
                await odoo_request._post_init()
                current_thread.url = str(request.url)
                
                # Handle static files
                if self.odoo_app.get_static_file(request.url.path):
                    return await self._serve_static(odoo_request)
                
                # Handle database requests
                elif odoo_request.db:
                    return await self._serve_db(odoo_request)
                else:
                    return await self._serve_nodb(odoo_request)
                    
            finally:
                http._request_stack.pop()
                
        except Exception:
            _logger.error("Exception during ASGI request handling.", exc_info=True)
            return StarletteResponse("Internal Server Error", status_code=500)
    
    async def _serve_static(self, odoo_request: ASGIRequest) -> StarletteResponse:
        """Handle static file serving"""
        file_path = self.odoo_app.get_static_file(odoo_request.httprequest.path)
        if file_path and os.path.isfile(file_path):
            return FileResponse(file_path)
        return StarletteResponse("File not found", status_code=404)

    async def _serve_db(self, odoo_request: ASGIRequest) -> StarletteResponse:
        """Handle database requests"""
        try:
            # For now, redirect to the traditional WSGI handling
            # This is a placeholder - full async DB implementation would go here
            return RedirectResponse(url="/web", status_code=302)
        except Exception:
            _logger.exception("Error serving database request")
            return StarletteResponse("Internal Server Error", status_code=500)

    async def _serve_nodb(self, odoo_request: ASGIRequest) -> StarletteResponse:
        """Handle no-database requests"""
        try:
            # Use the same routing logic as WSGI version
            router = http.root.nodb_routing_map.bind_to_environ(self._create_environ(odoo_request))
            try:
                rule, args = router.match(return_rule=True)
                odoo_request._set_request_dispatcher(rule)

                # Pre-dispatch
                odoo_request.dispatcher.pre_dispatch(rule, args)

                # Dispatch to endpoint
                response = odoo_request.dispatcher.dispatch(rule.endpoint, args)

                # Post-dispatch
                odoo_request.dispatcher.post_dispatch(response)

                # Convert response to ASGI format
                return await self._convert_response_to_asgi(response)

            except NotFound:
                # If no route found, redirect to database selector
                return RedirectResponse(url="/web/database/selector", status_code=303)

        except Exception:
            _logger.exception("Error serving no-database request")
            return StarletteResponse("Internal Server Error", status_code=500)

    def _create_environ(self, odoo_request: ASGIRequest) -> dict:
        """Create WSGI environ dict from ASGI request for compatibility"""
        environ = {
            'REQUEST_METHOD': odoo_request.httprequest.method,
            'PATH_INFO': odoo_request.httprequest.path,
            'QUERY_STRING': odoo_request.httprequest.query_string.decode() if odoo_request.httprequest.query_string else '',
            'CONTENT_TYPE': odoo_request.httprequest.headers.get('content-type', ''),
            'CONTENT_LENGTH': odoo_request.httprequest.headers.get('content-length', ''),
            'SERVER_NAME': odoo_request.starlette_request.url.hostname or 'localhost',
            'SERVER_PORT': str(odoo_request.starlette_request.url.port or 80),
            'HTTP_HOST': odoo_request.httprequest.headers.get('host', 'localhost'),
            'wsgi.url_scheme': odoo_request.starlette_request.url.scheme,
        }

        # Add HTTP headers
        for key, value in odoo_request.httprequest.headers.items():
            key = 'HTTP_' + key.upper().replace('-', '_')
            environ[key] = value

        return environ

    async def _convert_response_to_asgi(self, response):
        """Convert Odoo response to ASGI response"""
        if hasattr(response, 'status_code'):
            # It's already a proper response object
            if hasattr(response, 'headers'):
                headers = dict(response.headers)
            else:
                headers = {}

            if hasattr(response, 'data'):
                content = response.data
            elif hasattr(response, 'get_data'):
                content = response.get_data()
            else:
                content = str(response)

            return StarletteResponse(
                content=content,
                status_code=response.status_code,
                headers=headers
            )
        else:
            # Simple string response
            return StarletteResponse(content=str(response))


class ASGIApplication:
    """
    Modern ASGI application for Odoo
    """
    
    def __init__(self):
        self.starlette_app = None
        self._setup_application()
    
    def _setup_application(self):
        """Setup the Starlette ASGI application"""
        middleware = [
            Middleware(OdooASGIMiddleware, odoo_app=self)
        ]

        routes = [
            # WebSocket routes
            WebSocketRoute("/websocket", self._websocket_endpoint),
            WebSocketRoute("/websocket/{path:path}", self._websocket_endpoint),
            # Add other routes here as needed
        ]

        self.starlette_app = Starlette(
            routes=routes,
            middleware=middleware
        )
    
    @lazy_property
    def statics(self):
        """
        Map module names to their absolute ``static`` path on the file
        system. (Copied from original Application class)
        """
        from odoo.modules.module import get_manifest
        from os.path import join as opj
        import os
        
        mod2path = {}
        for addons_path in odoo.addons.__path__:
            for module in os.listdir(addons_path):
                manifest = get_manifest(module)
                static_path = opj(addons_path, module, 'static')
                if (manifest
                        and (manifest['installable'] or manifest['assets'])
                        and os.path.isdir(static_path)):
                    mod2path[module] = static_path
        return mod2path
    
    def get_static_file(self, url, host=''):
        """
        Get the full-path of the file if the url resolves to a local
        static file, otherwise return None. (Copied from original Application class)
        """
        from urllib.parse import urlparse
        
        netloc, path = urlparse(url)[1:3]
        try:
            path_netloc, module, static, resource = path.split('/', 3)
        except ValueError:
            return None

        if ((netloc and netloc != host) or (path_netloc and path_netloc != host)):
            return None

        if (module not in self.statics or static != 'static' or not resource):
            return None

        try:
            import os
            resource_path = os.path.join(self.statics[module], resource)
            if os.path.isfile(resource_path):
                return resource_path
        except (OSError, ValueError):
            pass
        return None

    async def _websocket_endpoint(self, websocket):
        """
        WebSocket endpoint handler
        """
        try:
            # Accept the WebSocket connection
            await websocket.accept()

            # Import here to avoid circular imports
            from odoo.addons.web.controllers.websocket import WebSocketController, connection_manager

            # Use the controller for authentication
            controller = WebSocketController()

            try:
                # Authenticate the connection
                user_id, db_name, session_id = controller._authenticate_websocket(websocket)

                # Register the connection
                await connection_manager.connect(websocket, user_id, db_name, session_id)

                # Send welcome message
                await websocket.send_text(json.dumps({
                    'type': 'welcome',
                    'user_id': user_id,
                    'db_name': db_name,
                    'timestamp': datetime.now().isoformat()
                }))

                # Handle messages
                while True:
                    # Receive message
                    message_text = await websocket.receive_text()

                    try:
                        message = json.loads(message_text)
                        await controller._handle_websocket_message(websocket, message, user_id, db_name)
                    except json.JSONDecodeError:
                        await websocket.send_text(json.dumps({
                            'type': 'error',
                            'message': 'Invalid JSON format'
                        }))

            except Exception as e:
                _logger.error(f"WebSocket error: {e}")
                await websocket.send_text(json.dumps({
                    'type': 'error',
                    'message': str(e)
                }))
            finally:
                # Disconnect the WebSocket
                await connection_manager.disconnect(websocket)

        except Exception as e:
            _logger.error(f"WebSocket endpoint error: {e}")
            try:
                await websocket.close(code=1011, reason="Internal server error")
            except:
                pass

    async def __call__(self, scope, receive, send):
        """
        ASGI application entry point
        """
        await self.starlette_app(scope, receive, send)


# Create the global ASGI application instance
asgi_root = ASGIApplication()
